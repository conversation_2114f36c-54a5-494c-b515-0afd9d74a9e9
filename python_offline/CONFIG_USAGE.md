# Configuration System Usage

The BSPML project now includes a flexible configuration system that allows you to easily customize preprocessing parameters for different datasets.

## Overview

The configuration system supports two main dataset types:
- `ppgDalia`: For PPG Dalia dataset
- `real`: For real-world data

Each dataset type has its own optimized parameters for:
- RLS filter (forgetting factor, filter order, etc.)
- Bandpass filter (cutoff frequencies, filter type, etc.)
- Wavelet detrending (wavelet type, levels, mode)
- Motion detection (threshold, adaptive settings)

## Basic Usage

### Loading Configuration

```python
from bspml import load_config, get_dataset_config

# Load default configuration
config = load_config()

# Load from custom file
config = load_config('path/to/your/config.json')

# Get dataset-specific configuration
ppg_config = get_dataset_config(config, 'ppgDalia')
real_config = get_dataset_config(config, 'real')
```

### Using Configuration in Pipeline

```python
from bspml import run_pipeline

# Run pipeline with explicit dataset type
results = run_pipeline(
    data_identifier="your_data",
    data_path="path/to/data",
    dataset_type="ppgDalia",  # or "real"
    config_path="config/custom_config.json"  # optional
)
```

### Manual Preprocessing with Configuration

```python
from bspml import preprocess_ppg, load_config, get_dataset_config

# Load configuration
config = load_config()
dataset_config = get_dataset_config(config, 'ppgDalia')

# Use configuration in preprocessing
processed_signal = preprocess_ppg(
    ppg_signal=your_ppg_signal,
    acc_signals=your_acc_signals,
    sampling_rate=50.0,
    enable_detrending=dataset_config['enable_detrending'],
    enable_denoising=dataset_config['enable_denoising'],
    enable_motion_removal=dataset_config['enable_motion_removal'],
    adaptive_motion_removal=dataset_config['adaptive_motion_removal'],
    motion_threshold=dataset_config['motion_threshold'],
    preprocessing_params={
        'rls': {
            'filter_order': dataset_config['filter_order'],
            'forgetting_factor': dataset_config['forgetting_factor'],
            'delay_compensation': dataset_config['delay_compensation'],
            'auto_delay_detection': dataset_config['auto_delay_detection'],
        },
        'bandpass': {
            'low_cutoff': dataset_config['low_cutoff'],
            'high_cutoff': dataset_config['high_cutoff'],
            'filter_order': dataset_config['bandpass_filter_order'],
            'filter_type': dataset_config['filter_type'],
        },
        'wavelet': {
            'wavelet': dataset_config['wavelet'],
            'levels': dataset_config['levels'],
            'mode': dataset_config['mode'],
        },
    },
)
```

## Configuration Parameters

### RLS Filter Parameters
- `forgetting_factor`: RLS forgetting factor (0 < λ ≤ 1)
- `filter_order`: Adaptive filter order
- `delay_compensation`: Manual delay compensation (samples)
- `auto_delay_detection`: Enable automatic delay detection

### Bandpass Filter Parameters
- `low_cutoff`: Low cutoff frequency (Hz)
- `high_cutoff`: High cutoff frequency (Hz)
- `bandpass_filter_order`: Filter order
- `filter_type`: Filter type ('butterworth', 'chebyshev1', 'elliptic')

### Wavelet Detrending Parameters
- `wavelet`: Wavelet type ('db4', 'db8', 'haar', etc.)
- `levels`: Decomposition levels
- `mode`: Signal extension mode ('symmetric', 'periodization', etc.)

### Motion Detection Parameters
- `motion_threshold`: Motion magnitude threshold
- `adaptive_motion_removal`: Enable adaptive motion removal

### Processing Flags
- `enable_detrending`: Enable wavelet detrending
- `enable_denoising`: Enable bandpass filtering
- `enable_motion_removal`: Enable RLS motion artifact removal

## Default Configurations

### PPG Dalia Dataset
- Forgetting factor: 0.999
- Filter order: 5
- Motion threshold: 50.0
- Optimized for controlled laboratory conditions

### Real World Dataset
- Forgetting factor: 0.995
- Filter order: 8
- Motion threshold: 30.0 (more sensitive)
- Optimized for real-world conditions with more artifacts

## Custom Configuration Files

You can create custom configuration files in JSON format:

```json
{
    "ppgDalia": {
        "forgetting_factor": 0.999,
        "filter_order": 5,
        "motion_threshold": 50.0,
        ...
    },
    "real": {
        "forgetting_factor": 0.995,
        "filter_order": 8,
        "motion_threshold": 30.0,
        ...
    }
}
```

## Utilities

### Print Configuration
```python
from bspml import print_config

# Print all configurations
print_config(config)

# Print specific dataset configuration
print_config(config, 'ppgDalia')
```

### Save Configuration
```python
from bspml.config import save_config

save_config(config, 'path/to/save/config.json')
```

## Migration from Old System

The old system used hardcoded parameters and **kwargs. The new system:
1. Replaces hardcoded values with configuration-based parameters
2. Eliminates **kwargs in favor of structured configuration
3. Provides dataset-specific optimizations
4. Maintains backward compatibility through default configurations

## Examples

See `test_config.py` for complete working examples of the configuration system.
