#!/usr/bin/env python3
"""
Test script for the new configuration system.

This script demonstrates how to use the new config-based preprocessing pipeline.
"""

import numpy as np
from bspml import (
    load_config, 
    get_dataset_config, 
    print_config, 
    preprocess_ppg
)


def test_config_loading():
    """Test configuration loading and display."""
    print("=== Testing Configuration Loading ===")
    
    # Load default configuration
    config = load_config()
    print("✓ Default configuration loaded successfully")
    
    # Load from file
    config_from_file = load_config('config/default_config.json')
    print("✓ Configuration loaded from file successfully")
    
    # Test dataset-specific configs
    ppg_config = get_dataset_config(config, 'ppgDalia')
    real_config = get_dataset_config(config, 'real')
    
    print(f"✓ PPG Dalia forgetting factor: {ppg_config['forgetting_factor']}")
    print(f"✓ Real world forgetting factor: {real_config['forgetting_factor']}")
    
    return config


def test_preprocessing_with_config():
    """Test preprocessing with configuration parameters."""
    print("\n=== Testing Preprocessing with Config ===")
    
    # Create synthetic test data
    np.random.seed(42)  # For reproducible results
    duration = 10  # seconds
    fs = 50  # Hz
    n_samples = duration * fs
    
    # Generate synthetic PPG signal (heart rate around 70 BPM)
    t = np.linspace(0, duration, n_samples)
    hr_signal = np.sin(2 * np.pi * 1.17 * t)  # ~70 BPM
    noise = np.random.randn(n_samples) * 0.1
    ppg_signal = hr_signal + noise
    
    # Generate synthetic accelerometer data
    acc_signals = np.random.randn(n_samples, 3) * 0.5
    
    # Load configuration
    config = load_config()
    
    # Test with PPG Dalia configuration
    print("Testing with PPG Dalia configuration...")
    ppg_config = get_dataset_config(config, 'ppgDalia')
    
    processed_ppg = preprocess_ppg(
        ppg_signal=ppg_signal,
        acc_signals=acc_signals,
        sampling_rate=fs,
        acc_sampling_rate=32.0,
        enable_detrending=ppg_config['enable_detrending'],
        enable_denoising=ppg_config['enable_denoising'],
        enable_motion_removal=ppg_config['enable_motion_removal'],
        adaptive_motion_removal=ppg_config['adaptive_motion_removal'],
        motion_threshold=ppg_config['motion_threshold'],
        preprocessing_params={
            'rls': {
                'filter_order': ppg_config['filter_order'],
                'forgetting_factor': ppg_config['forgetting_factor'],
                'delay_compensation': ppg_config['delay_compensation'],
                'auto_delay_detection': ppg_config['auto_delay_detection'],
            },
            'bandpass': {
                'low_cutoff': ppg_config['low_cutoff'],
                'high_cutoff': ppg_config['high_cutoff'],
                'filter_order': ppg_config['bandpass_filter_order'],
                'filter_type': ppg_config['filter_type'],
            },
            'wavelet': {
                'wavelet': ppg_config['wavelet'],
                'levels': ppg_config['levels'],
                'mode': ppg_config['mode'],
            },
        },
    )
    
    print(f"✓ PPG Dalia preprocessing successful")
    print(f"  Input length: {len(ppg_signal)}, Output length: {len(processed_ppg)}")
    
    # Test with Real world configuration
    print("\nTesting with Real world configuration...")
    real_config = get_dataset_config(config, 'real')
    
    processed_real = preprocess_ppg(
        ppg_signal=ppg_signal,
        acc_signals=acc_signals,
        sampling_rate=fs,
        acc_sampling_rate=32.0,
        enable_detrending=real_config['enable_detrending'],
        enable_denoising=real_config['enable_denoising'],
        enable_motion_removal=real_config['enable_motion_removal'],
        adaptive_motion_removal=real_config['adaptive_motion_removal'],
        motion_threshold=real_config['motion_threshold'],
        preprocessing_params={
            'rls': {
                'filter_order': real_config['filter_order'],
                'forgetting_factor': real_config['forgetting_factor'],
                'delay_compensation': real_config['delay_compensation'],
                'auto_delay_detection': real_config['auto_delay_detection'],
            },
            'bandpass': {
                'low_cutoff': real_config['low_cutoff'],
                'high_cutoff': real_config['high_cutoff'],
                'filter_order': real_config['bandpass_filter_order'],
                'filter_type': real_config['filter_type'],
            },
            'wavelet': {
                'wavelet': real_config['wavelet'],
                'levels': real_config['levels'],
                'mode': real_config['mode'],
            },
        },
    )
    
    print(f"✓ Real world preprocessing successful")
    print(f"  Input length: {len(ppg_signal)}, Output length: {len(processed_real)}")
    
    return processed_ppg, processed_real


def main():
    """Main test function."""
    print("Configuration System Test")
    print("=" * 50)
    
    try:
        # Test configuration loading
        config = test_config_loading()
        
        # Test preprocessing
        processed_ppg, processed_real = test_preprocessing_with_config()
        
        print("\n=== Configuration Details ===")
        print_config(config)
        
        print("\n" + "=" * 50)
        print("✅ All tests passed successfully!")
        print("The new configuration system is working correctly.")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        raise


if __name__ == "__main__":
    main()
