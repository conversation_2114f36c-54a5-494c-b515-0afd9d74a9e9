{"ppgDalia": {"forgetting_factor": 0.999, "filter_order": 5, "delay_compensation": null, "auto_delay_detection": true, "low_cutoff": 0.5, "high_cutoff": 4.0, "bandpass_filter_order": 4, "filter_type": "butterworth", "wavelet": "db4", "levels": 6, "mode": "symmetric", "motion_threshold": 50.0, "adaptive_motion_removal": true, "enable_detrending": true, "enable_denoising": true, "enable_motion_removal": true, "peak_detection_method": "sliding_window", "window_size": 10.0, "overlap": 0.3, "min_peak_distance": 0.32, "prominence": 0.55, "min_peak_height": 0, "adaptive_threshold": true, "use_envelope_method": false, "interpolation_rate": 0.6, "return_peaks": true, "max_peak_distance": 2.0, "prominence_factor": 0.5}, "real": {"forgetting_factor": 0.999, "filter_order": 5, "delay_compensation": null, "auto_delay_detection": true, "low_cutoff": 0.5, "high_cutoff": 4.0, "bandpass_filter_order": 4, "filter_type": "butterworth", "wavelet": "db4", "levels": 6, "mode": "symmetric", "motion_threshold": 50.0, "adaptive_motion_removal": true, "enable_detrending": true, "enable_denoising": true, "enable_motion_removal": true, "peak_detection_method": "sliding_window", "window_size": 10.0, "overlap": 0.3, "min_peak_distance": 0.5, "prominence": 0.6, "min_peak_height": 0, "adaptive_threshold": true, "use_envelope_method": false, "interpolation_rate": 0.8, "return_peaks": true, "max_peak_distance": 1.8, "prominence_factor": 0.55}}