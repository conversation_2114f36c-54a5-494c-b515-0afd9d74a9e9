"""
Configuration Module

Provides configuration management for PPG signal processing parameters
for different datasets (ppgDalia and real-world data).
"""

import json
import os
from typing import Dict, Any, Optional


def get_default_config() -> Dict[str, Any]:
    """
    Get default configuration for all datasets.

    Returns:
        Dictionary containing default configuration parameters
    """
    return {
        "ppgDalia": {
            # RLS Filter parameters
            "forgetting_factor": 0.999,
            "filter_order": 5,
            "delay_compensation": None,
            "auto_delay_detection": True,

            # Bandpass filter parameters
            "low_cutoff": 0.5,
            "high_cutoff": 4.0,
            "bandpass_filter_order": 4,
            "filter_type": "butterworth",

            # Wavelet detrending parameters
            "wavelet": "db4",
            "levels": 6,
            "mode": "symmetric",

            # Motion detection parameters
            "motion_threshold": 50.0,
            "adaptive_motion_removal": True,

            # Processing flags
            "enable_detrending": True,
            "enable_denoising": True,
            "enable_motion_removal": True
        },
        "real": {
            # RLS Filter parameters (different settings for real-world data)
            "forgetting_factor": 0.995,
            "filter_order": 8,
            "delay_compensation": None,
            "auto_delay_detection": True,

            # Bandpass filter parameters
            "low_cutoff": 0.5,
            "high_cutoff": 4.0,
            "bandpass_filter_order": 4,
            "filter_type": "butterworth",

            # Wavelet detrending parameters
            "wavelet": "db4",
            "levels": 6,
            "mode": "symmetric",

            # Motion detection parameters (more sensitive for real-world data)
            "motion_threshold": 30.0,
            "adaptive_motion_removal": True,

            # Processing flags
            "enable_detrending": True,
            "enable_denoising": True,
            "enable_motion_removal": True
        }
    }


def load_config(config_path: Optional[str] = None) -> Dict[str, Any]:
    """
    Load configuration from file or return default configuration.

    Args:
        config_path: Path to configuration file (JSON format)

    Returns:
        Configuration dictionary
    """
    if config_path is None or not os.path.exists(config_path):
        return get_default_config()

    try:
        with open(config_path, 'r') as f:
            config = json.load(f)

        # Merge with defaults to ensure all required keys exist
        default_config = get_default_config()
        for dataset in default_config:
            if dataset not in config:
                config[dataset] = default_config[dataset]
            else:
                # Update missing keys with defaults
                for key, value in default_config[dataset].items():
                    if key not in config[dataset]:
                        config[dataset][key] = value

        return config

    except (json.JSONDecodeError, IOError) as e:
        print(f"Warning: Could not load config from {config_path}: {e}")
        print("Using default configuration.")
        return get_default_config()


def save_config(config: Dict[str, Any], config_path: str) -> None:
    """
    Save configuration to file.

    Args:
        config: Configuration dictionary
        config_path: Path to save configuration file
    """
    try:
        os.makedirs(os.path.dirname(config_path), exist_ok=True)
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=4)
        print(f"Configuration saved to {config_path}")
    except IOError as e:
        print(f"Error saving configuration to {config_path}: {e}")


def get_dataset_config(config: Dict[str, Any], dataset_type: str) -> Dict[str, Any]:
    """
    Get configuration for a specific dataset type.

    Args:
        config: Full configuration dictionary
        dataset_type: Dataset type ('ppgDalia' or 'real')

    Returns:
        Configuration dictionary for the specified dataset

    Raises:
        ValueError: If dataset_type is not supported
    """
    if dataset_type not in config:
        raise ValueError(f"Unsupported dataset type: {dataset_type}. "
                         f"Supported types: {list(config.keys())}")

    return config[dataset_type]


def print_config(config: Dict[str, Any], dataset_type: Optional[str] = None) -> None:
    """
    Print configuration parameters in a readable format.

    Args:
        config: Configuration dictionary
        dataset_type: Specific dataset type to print (None for all)
    """
    if dataset_type is not None:
        if dataset_type not in config:
            print(f"Dataset type '{dataset_type}' not found in configuration")
            return
        datasets = [dataset_type]
    else:
        datasets = list(config.keys())

    for dataset in datasets:
        print(f"\n=== {dataset.upper()} Configuration ===")
        dataset_config = config[dataset]

        print("RLS Filter:")
        print(f"  - Forgetting factor: {dataset_config['forgetting_factor']}")
        print(f"  - Filter order: {dataset_config['filter_order']}")
        print(
            f"  - Auto delay detection: {dataset_config['auto_delay_detection']}")

        print("Bandpass Filter:")
        print(f"  - Low cutoff: {dataset_config['low_cutoff']} Hz")
        print(f"  - High cutoff: {dataset_config['high_cutoff']} Hz")
        print(f"  - Filter order: {dataset_config['bandpass_filter_order']}")
        print(f"  - Filter type: {dataset_config['filter_type']}")

        print("Wavelet Detrending:")
        print(f"  - Wavelet: {dataset_config['wavelet']}")
        print(f"  - Levels: {dataset_config['levels']}")
        print(f"  - Mode: {dataset_config['mode']}")

        print("Motion Detection:")
        print(f"  - Motion threshold: {dataset_config['motion_threshold']}")
        print(
            f"  - Adaptive motion removal: {dataset_config['adaptive_motion_removal']}")

        print("Processing Flags:")
        print(f"  - Enable detrending: {dataset_config['enable_detrending']}")
        print(f"  - Enable denoising: {dataset_config['enable_denoising']}")
        print(
            f"  - Enable motion removal: {dataset_config['enable_motion_removal']}")
